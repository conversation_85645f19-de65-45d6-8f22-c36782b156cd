{"name": "discord-bot-nestjs", "version": "1.0.0", "description": "NestJS Discord Bot with REST API", "author": "", "private": true, "license": "MIT", "scripts": {"build": "nest build", "build:force": "tsc --noEmitOnError false --skipL<PERSON><PERSON><PERSON><PERSON> || echo 'Build completed with errors, but continuing...'", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d src/core/database/data-source.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d src/core/database/data-source.ts", "schema:sync": "typeorm-ts-node-commonjs schema:sync -d src/core/database/data-source.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "^1.3.23", "@discordjs/voice": "^0.16.1", "@mastra/core": "^0.11.0", "@mastra/engine": "0.1.0-alpha.84", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/terminus": "^10.3.0", "@nestjs/typeorm": "^10.0.0", "@whop/api": "^0.0.36", "ai": "^4.3.19", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "discord-api-types": "^0.37.120", "discord.js": "^14.21.0", "dotenv": "^16.3.1", "express": "^4.21.2", "ffmpeg-static": "^5.2.0", "necord": "6.0.0", "node-cron": "^4.2.1", "passport": "^0.6.0", "passport-discord": "^0.1.4", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.1", "sodium": "^3.0.2", "typeorm": "^0.3.17", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/node-cron": "^3.0.11", "@types/passport-discord": "^0.1.14", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/pg": "^8.15.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=18.17.0"}, "packageManager": "pnpm@10.12.4"}